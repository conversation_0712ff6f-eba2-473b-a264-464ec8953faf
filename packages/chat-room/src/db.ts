import <PERSON>ie, { type EntityTable } from "dexie";
import { v7 } from "uuid";

export type Conversation = {
  id: string;
  createdAt: number;
  lastMessageTime: number;
};

export type Message = {
  id: string;
  conversationId: string;
  content: string;
  isUserMessage: boolean;
  timestamp: number;
};

type Client = {
  id: string;
};

export const db = new Dexie("ChatRoomDB") as <PERSON><PERSON> & {
  conversations: EntityTable<Conversation, "id">;
  messages: EntityTable<Message, "id">;
  client: EntityTable<Client, "id">;
};

db.version(1).stores({
  conversations: "&id, createdAt, lastMessageTime",
  messages: "&id, conversationId, timestamp",
  client: "&id",
});

db.on("ready", () => {
  return new Promise<void>((resolve, reject) => {
    db.client
      .limit(1)
      .first()
      .then((client) => {
        if (!client) {
          const newClient: Client = { id: v7() }; // Generate a new client ID
          db.client
            .put(newClient)
            .catch((error) => {
              console.error("Failed to initialize client ID:", error);
              reject(error);
            })
            .then(() => {
              console.log("Client initialized with ID:", newClient.id);
              resolve();
            });
        } else {
          resolve();
        }
      });
  });
});
