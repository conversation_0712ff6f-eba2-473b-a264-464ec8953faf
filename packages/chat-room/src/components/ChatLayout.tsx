import { useConversations } from "@/hooks/useConversations";
import { useState } from "react";
import { useChat } from "../hooks/useChat";
import { ChatArea } from "./ChatArea";
import { Sidebar } from "./Sidebar";

export const ChatLayout = () => {
  const {
    conversations,
    setSelectedConversationId,
    selectedConversationId,
    addConversation,
    selectedConversation,
  } = useConversations();

  const { messages, sendMessage, isBotTyping } = useChat(
    selectedConversationId
  );

  console.log("Selected conversation:", selectedConversation);
  console.log("Messages:", messages);

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleConversationSelect = (conversationId: string) => {
    setSelectedConversationId(conversationId);
    // Close mobile menu when conversation is selected
    setIsMobileMenuOpen(false);
  };

  const handleNewConversation = () => {
    addConversation();
    // Close mobile menu when new conversation is created
    setIsMobileMenuOpen(false);
  };

  return (
    <div className="h-screen bg-base-200">
      {/* Mobile drawer */}
      <div className="drawer lg:drawer-open">
        <input
          id="mobile-drawer"
          type="checkbox"
          className="drawer-toggle"
          checked={isMobileMenuOpen}
          onChange={(e) => setIsMobileMenuOpen(e.target.checked)}
        />

        {/* Main content */}
        <div className="drawer-content flex flex-col">
          {/* Mobile header with menu button - STICKY */}
          <div className="navbar bg-base-100 lg:hidden border-b border-base-300 sticky top-0 z-50">
            <div className="flex-none">
              <label
                htmlFor="mobile-drawer"
                className="btn btn-square btn-ghost"
                aria-label="Open menu"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  className="inline-block w-6 h-6 stroke-current"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </label>
            </div>
            <div className="flex-1">
              <h1 className="text-lg font-bold">
                {import.meta.env.VITE_BOT_NAME || "AI Assistant"}
              </h1>
            </div>
          </div>

          {/* Chat area */}
          <div className="flex-1 min-h-0">
            <ChatArea
              conversation={selectedConversation}
              messages={messages}
              onSendMessage={sendMessage}
              onNewConversation={handleNewConversation}
              conversations={conversations}
              isBotTyping={isBotTyping}
            />
          </div>
        </div>

        {/* Sidebar */}
        <div className="drawer-side">
          <label
            htmlFor="mobile-drawer"
            className="drawer-overlay"
            aria-label="Close menu"
          />
          <aside className="w-80 min-h-full bg-base-100">
            <Sidebar
              conversations={conversations}
              selectedConversationId={selectedConversationId}
              onConversationSelect={handleConversationSelect}
              onNewConversation={handleNewConversation}
            />
          </aside>
        </div>
      </div>
    </div>
  );
};
