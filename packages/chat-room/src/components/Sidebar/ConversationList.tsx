import type { Conversation } from "@/db";
import { ConversationItem } from "./ConversationItem";

interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId: string | null;
  onConversationSelect: (conversationId: string) => void;
  onNewConversation: () => void;
}

export const ConversationList = ({
  conversations,
  selectedConversationId,
  onConversationSelect,
  onNewConversation,
}: ConversationListProps) => {
  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Fixed Header - contains title and new conversation button */}
      <div className="flex-shrink-0 bg-base-100 border-b border-base-300">
        {/* Header */}
        <div className="p-4 border-b border-base-300">
          <h2 className="text-lg font-bold text-base-content">Conversations</h2>
          <p className="text-sm text-base-content/60">
            {conversations.length} conversation
            {conversations.length !== 1 ? "s" : ""}
          </p>
        </div>

        {/* New Conversation Button */}
        <div className="p-4">
          <button
            className="btn btn-primary btn-block"
            onClick={onNewConversation}
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
            New Conversation
          </button>
        </div>
      </div>

      {/* Scrollable Conversations List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2 space-y-2">
          {conversations.length === 0 ? (
            <div className="text-center text-base-content/60 py-8">
              <p>No conversations yet</p>
              <p className="text-sm">Start a new conversation above!</p>
            </div>
          ) : (
            conversations.map((conversation) => (
              <ConversationItem
                key={conversation.id}
                conversation={conversation}
                isSelected={selectedConversationId === conversation.id}
                onClick={() => onConversationSelect(conversation.id)}
              />
            ))
          )}
        </div>
      </div>

      {/* Fixed Footer */}
      <div className="flex-shrink-0 p-4 border-t border-base-300">
        <div className="text-xs text-base-content/50 text-center">
          {import.meta.env.VITE_BOT_NAME} Chat ⚡️
        </div>
      </div>
    </div>
  );
};
