import type { Conversation } from "@/db";
import { formatConversationDate } from "../../utils/dateFormatter";

interface ConversationItemProps {
  conversation: Conversation;
  isSelected: boolean;
  onClick: () => void;
}

export const ConversationItem = ({
  conversation,
  isSelected,
  onClick,
}: ConversationItemProps) => {
  return (
    <div
      className={`card cursor-pointer transition-all duration-200 hover:bg-base-200 ${
        isSelected ? "bg-primary/10 border-primary/20" : "bg-base-100"
      }`}
      onClick={onClick}
    >
      <div className="card-body p-4">
        <div className="flex items-center justify-center">
          <h3
            className={`font-medium text-center ${
              isSelected ? "text-primary" : "text-base-content"
            }`}
          >
            {formatConversationDate(conversation.createdAt)}
          </h3>
        </div>
      </div>
    </div>
  );
};
