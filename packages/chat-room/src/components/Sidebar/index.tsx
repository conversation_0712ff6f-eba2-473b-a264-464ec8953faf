import type { Conversation } from "@/db";
import { ConversationList } from "./ConversationList";

interface SidebarProps {
  conversations: Conversation[];
  selectedConversationId: string | null;
  onConversationSelect: (conversationId: string) => void;
  onNewConversation: () => void;
}

export const Sidebar = ({
  conversations,
  selectedConversationId,
  onConversationSelect,
  onNewConversation,
}: SidebarProps) => {
  return (
    <div className="h-full bg-base-100 border-r border-base-300">
      <ConversationList
        conversations={conversations}
        selectedConversationId={selectedConversationId}
        onConversationSelect={onConversationSelect}
        onNewConversation={onNewConversation}
      />
    </div>
  );
};
