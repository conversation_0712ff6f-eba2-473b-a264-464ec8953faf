import type { Conversation } from "@/db";
import { formatConversationDate } from "../../utils/dateFormatter";

interface ChatHeaderProps {
  conversation?: Conversation | null;
}

export const ChatHeader = ({ conversation }: ChatHeaderProps) => {
  if (!conversation) {
    return (
      <div className="navbar bg-base-100 border-b border-base-300 px-4">
        <div className="flex-1">
          <h1 className="text-lg font-bold text-base-content/50">
            Select a conversation to start chatting
          </h1>
        </div>
      </div>
    );
  }

  return (
    <div className="navbar bg-base-100 border-b border-base-300 px-4">
      <div className="flex-1">
        <div className="flex items-center gap-3">
          {/* Bot Avatar */}
          <div className="avatar placeholder">
            <div className="bg-primary text-primary-content rounded-full w-12 h-12">
              <img src={import.meta.env.VITE_BOT_AVATAR || "/default-avatar.png"} alt="Bot Avatar" />
            </div>
          </div>

          {/* Conversation Info */}
          <div>
            <h1 className="text-lg font-bold text-base-content">
              {import.meta.env.VITE_BOT_NAME || "AI Assistant"}
            </h1>
            <p className="text-sm text-base-content/60">
              Conversation from {formatConversationDate(conversation.createdAt)}
            </p>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex-none">
        <div className="dropdown dropdown-end">
          <div tabIndex={0} role="button" className="btn btn-ghost btn-circle">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              className="w-5 h-5 stroke-current"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 6v.01M12 12v.01M12 18v.01"
              />
            </svg>
          </div>
          <ul
            tabIndex={0}
            className="menu dropdown-content bg-base-100 rounded-box z-[1] w-52 p-2 shadow border border-base-300"
          >
            <li>
              <a>Clear Conversation</a>
            </li>
            <li>
              <a>Export Chat</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};
