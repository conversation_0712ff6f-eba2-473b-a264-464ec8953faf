import { useState } from "react";
import { v7 } from "uuid";

interface MessageInputProps {
  onSend: (message: string, messageId?: string) => void;
  placeholder?: string;
}

export const MessageInput = ({
  onSend,
  placeholder = "Type a message...",
}: MessageInputProps) => {
  const [isTyping, setIsTyping] = useState(false);
  const [value, setValue] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (value.trim()) {
      onSend(value, v7());
      setValue("");
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    setIsTyping(newValue.length > 0);
  };

  const characterCount = value.length;
  const maxLength = 1000;

  return (
    <div className="border-t border-base-300 bg-base-100 p-4">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="flex-1">
          <textarea
            value={value}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="textarea textarea-bordered w-full resize-none min-h-[2.5rem] max-h-32"
            rows={1}
            autoFocus={true}
            maxLength={maxLength}
            style={{
              minHeight: "2.5rem",
              height:
                Math.min(Math.max(value.split("\n").length * 1.5, 2.5), 8) +
                "rem",
            }}
          />
          {characterCount > 800 && (
            <div className="text-xs text-base-content/60 mt-1 text-right">
              {characterCount}/{maxLength}
            </div>
          )}
        </div>

        <button
          type="submit"
          disabled={!value.trim()}
          className={`btn btn-primary ${
            value.trim() ? "" : "btn-disabled"
          } self-end`}
          aria-label="Send message"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-5 h-5"
          >
            <path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" />
          </svg>
        </button>
      </form>

      {isTyping && (
        <div className="text-xs text-base-content/50 mt-2">
          Press Enter to send, Shift+Enter for new line
        </div>
      )}
    </div>
  );
};
