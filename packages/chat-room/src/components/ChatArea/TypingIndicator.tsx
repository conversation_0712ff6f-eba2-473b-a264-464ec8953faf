import React from "react";

export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex items-start gap-2 animate-fadeIn">
      <div className="w-8 h-8 rounded-full bg-base-300 flex items-center justify-center">
        <span className="text-xs">
          <img
            src={import.meta.env.VITE_BOT_AVATAR || "/default-avatar.png"}
            alt="Bot Avatar"
            className="w-6 h-6 rounded-full"
          />
        </span>
      </div>
      <div className="chat-bubble chat-bubble-secondary">
        <div className="flex gap-1">
          <span className="typing-dot">●</span>
          <span className="typing-dot animation-delay-200">●</span>
          <span className="typing-dot animation-delay-400">●</span>
        </div>
      </div>
    </div>
  );
};
