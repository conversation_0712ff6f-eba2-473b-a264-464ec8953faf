import type { Message } from "@/db";
import MarkdownPreview from "@uiw/react-markdown-preview";

interface MessageBubbleProps {
  message: Message;
}

export const MessageBubble = ({ message }: MessageBubbleProps) => {
  const formatTime = (timestamp: string | number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const cleanContent = (content: string): string => {
    // Remove [1], [2], etc. patterns from the content
    return content.replace(/\[\d+\]/g, "");
  };

  const sender = message.isUserMessage
    ? "You"
    : import.meta.env.VITE_BOT_NAME || "AI Assistant";

  return (
    <div
      className={`chat ${message.isUserMessage ? "chat-end" : "chat-start"}`}
    >
      {/* Avatar */}
      <div className="chat-image avatar">
        <div className="w-10 rounded-full">
          <div className="bg-neutral text-neutral-content rounded-full w-10 h-10 flex items-center justify-center">
            <span className="text-sm font-medium">
              {sender.charAt(0).toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Sender name */}
      <div className="chat-header">
        <span className="text-sm font-medium text-base-content/70">
          {sender}
        </span>
        <time className="text-xs opacity-50 ml-2">
          {formatTime(message.timestamp)}
        </time>
      </div>

      {/* Message content */}
      <div
        className={`chat-bubble ${
          message.isUserMessage
            ? "chat-bubble-primary"
            : "chat-bubble-secondary"
        }`}
      >
        <MarkdownPreview
          source={cleanContent(message.content)}
          style={{
            backgroundColor: "transparent",
            color: "inherit",
            fontSize: "inherit",
            fontFamily: "inherit",
          }}
          wrapperElement={{
            "data-color-mode": "light",
          }}
        />
      </div>
    </div>
  );
};
