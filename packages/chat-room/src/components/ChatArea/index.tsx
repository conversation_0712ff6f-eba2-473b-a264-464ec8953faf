import type { Conversation, Message } from "@/db";
import { <PERSON><PERSON><PERSON>eader } from "./ChatHeader";
import { MessageInput } from "./MessageInput";
import { MessageList } from "./MessageList";
interface ChatAreaProps {
  conversation?: Conversation | null;
  messages: Message[];
  onSendMessage: (message: string, messageId?: string) => void;
  onNewConversation?: () => void;
  conversations: Conversation[];
  isBotTyping?: boolean;
}

export const ChatArea = ({
  conversation,
  messages,
  onSendMessage,
  onNewConversation,
  conversations,
  isBotTyping = false,
}: ChatAreaProps) => {
  // Show welcome empty state when no conversations exist
  if (!conversation && conversations.length === 0 && onNewConversation) {
    return (
      <div className="flex flex-col h-full bg-base-100">
        <ChatHeader conversation={null} />
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <div className="text-8xl mb-6">
              <img
                src={import.meta.env.VITE_BOT_AVATAR || "/bot-icon.png"}
                alt="Bot Icon"
                className="w-64 h-64 mx-auto"
              />
            </div>
            <h2 className="text-2xl font-bold text-base-content mb-4">
              Welcome to {import.meta.env.VITE_BOT_NAME || "AI Assistant"} Chat!
            </h2>
            <p className="text-base text-base-content/70 mb-8">
              Start a conversation to begin chatting with your{" "}
              {import.meta.env.VITE_BOT_NAME || "AI Assistant"}.
            </p>
            <button
              onClick={onNewConversation}
              className="btn btn-primary btn-lg"
            >
              Start your first conversation
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-base-100">
      {/* Header */}
      <ChatHeader conversation={conversation} />

      {/* Messages */}
      <MessageList messages={messages} isBotTyping={isBotTyping} />

      {conversation && (
        // Input area only shows if a conversation is selected
        <MessageInput
          onSend={onSendMessage}
          placeholder={
            conversation
              ? "Ask me anything..."
              : "Select a conversation to start chatting"
          }
        />
      )}
    </div>
  );
};
