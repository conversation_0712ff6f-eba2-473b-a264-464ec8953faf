@import "tailwindcss";
@plugin "daisyui";

/* Custom styles for markdown preview in chat bubbles */
.chat-bubble .wmde-markdown {
  background-color: transparent !important;
  color: inherit !important;
  font-size: inherit !important;
  font-family: inherit !important;
}

.chat-bubble .wmde-markdown pre {
  margin: 0.5rem 0;
  border-radius: 0.375rem;
  background-color: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-bubble .wmde-markdown code {
  font-size: 0.875rem;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.chat-bubble .wmde-markdown pre code {
  background-color: transparent;
  padding: 0;
}

/* Ensure proper spacing */
.chat-bubble .wmde-markdown > *:first-child {
  margin-top: 0;
}

.chat-bubble .wmde-markdown > *:last-child {
  margin-bottom: 0;
}

/* Table styles */
.chat-bubble .wmde-markdown table {
  font-size: 0.875rem;
  border-collapse: collapse;
  margin: 0.5rem 0;
}

.chat-bubble .wmde-markdown th,
.chat-bubble .wmde-markdown td {
  padding: 0.25rem 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.chat-bubble .wmde-markdown th {
  background-color: rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

/* List styles */
.chat-bubble .wmde-markdown ul,
.chat-bubble .wmde-markdown ol {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

/* Blockquote styles */
.chat-bubble .wmde-markdown blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.2);
  padding-left: 1rem;
  margin: 0.5rem 0;
  font-style: italic;
}

/* Link styles */
.chat-bubble .wmde-markdown a {
  color: inherit;
  text-decoration: underline;
  opacity: 0.8;
}

.chat-bubble .wmde-markdown a:hover {
  opacity: 1;
}

/* Typing indicator animations */
@keyframes bounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-dot {
  animation: bounce 1.4s infinite;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}