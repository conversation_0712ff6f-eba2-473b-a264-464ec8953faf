import type { Conversation } from "@/db";
import { db } from "@/db";
import { useLiveQuery } from "dexie-react-hooks";
import { useEffect, useMemo, useState } from "react";
import { v7 } from "uuid";

export function useConversations() {
  const conversations = useLiveQuery(() => db.conversations.toArray()) ?? [];

  conversations.sort((a, b) => b.lastMessageTime - a.lastMessageTime);

  const addConversation = async () => {
    const newConversation: Conversation = {
      id: v7(),
      createdAt: Date.now(),
      lastMessageTime: Date.now(),
    };

    await db.conversations.add(newConversation);
    setSelectedConversationId(newConversation.id);
  };

  const [selectedConversationId, setSelectedConversationId] = useState<
    string | null
  >(null);

  // Auto-select the latest conversation when conversations are loaded
  useEffect(() => {
    if (!selectedConversationId && conversations.length > 0) {
      setSelectedConversationId(conversations[0].id);
    }
  }, [conversations, selectedConversationId]);

  const selectedConversation = useMemo(() => {
    if (!selectedConversationId) return null;

    return (
      conversations.find(
        (conversation) => conversation.id === selectedConversationId
      ) || null
    );
  }, [selectedConversationId, conversations]);

  return {
    conversations,
    addConversation,
    selectedConversationId,
    setSelectedConversationId,
    selectedConversation,
  };
}
