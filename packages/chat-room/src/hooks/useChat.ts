import { db, type Message } from "@/db";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useAsyncEffect, useEventEmitter } from "ahooks";
import { useLiveQuery } from "dexie-react-hooks";
import { useEffect, useMemo, useRef, useState } from "react";
import useWebSocket from "react-use-websocket";
import { v7 } from "uuid";
type EventMessage = {
  type: string;
  value?: any;
};

type ConversationMessage = {
  id: string;
  content:
    | string
    | {
        type: string;
        text: string;
      }[];
  isUser: boolean;
  timestamp: number;
};

type ConversationWithMessages = {
  conversation: {
    id: string;
    channelType: string;
    timestamp: number;
  };
  messages: ConversationMessage[];
};
export const useChat = (conversationId: string | null) => {
  const [isBotTyping, setIsBotTyping] = useState(false);
  const typingTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const messages = useLiveQuery(async () => {
    if (!conversationId) {
      return [];
    }

    return db.messages.where("conversationId").equals(conversationId).toArray();
  }, [conversationId]);

  const client = useLiveQuery(() => db.client.limit(1).first());

  const { data, refetch } = useQuery({
    queryKey: ["chats", conversationId],
    queryFn: async () => {
      const resp = await fetch(
        `https://chatbot-server.rumput-tetangga.workers.dev/rooms/${conversationId}/messages`
      );

      if (!resp.ok) {
        throw new Error("Failed to fetch messages");
      }

      const data = (await resp.json()) as {
        status: string;
        data: ConversationWithMessages;
      };

      return data.data;
    },
    enabled: !!conversationId,
  });

  const { mutate } = useMutation({
    mutationKey: ["sendMessage", conversationId, client?.id],
    mutationFn: async (msg: Message) => {
      const resp = await fetch(
        `https://chatbot-server.rumput-tetangga.workers.dev/rooms/${conversationId}/messages`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: msg.content,
            messageId: msg.id,
          }),
        }
      );

      if (!resp.ok) {
        throw new Error("Failed to send message");
      }

      return resp.json();
    },
  });

  const { mutate: putMessage } = useMutation({
    mutationKey: ["put-message", client?.id],
    mutationFn: async (message: Message) => {
      console.log("Putting message:", message);

      event$.emit({
        type: "send-chat",
        value: message,
      });

      await db.messages.put(message);
    },
  });

  const event$ = useEventEmitter<EventMessage>();

  event$.useSubscription((msg) => {
    console.log("Event received:", msg);
    if (msg.type === "refresh") {
      refetch();
    }

    if (msg.type === "send-chat") {
      mutate(msg.value);
    }
  });

  useWebSocket(
    `wss://chatbot-server.rumput-tetangga.workers.dev/ws/rooms/${conversationId}`,
    {
      queryParams: { clientId: client?.id! },
      heartbeat: true,
      onOpen: () => {
        console.log("WebSocket connection established");
      },
      onClose: () => {
        console.log("WebSocket connection closed");
      },
      onError: (error) => {
        console.error("WebSocket error:", error);
      },
      onMessage: (message) => {
        console.log("WebSocket message received:", message);
        if (message.data === "pong") {
          return; // Ignore pong messages
        }

        const parsedMessage = JSON.parse(message.data) as { type: string };
        
        if (parsedMessage.type === "bot_typing_start") {
          setIsBotTyping(true);
          
          // Clear existing timeout
          if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
          }
          
          // Set 5-minute timeout
          typingTimeoutRef.current = setTimeout(() => {
            setIsBotTyping(false);
          }, 5 * 60 * 1000);
        }
        
        if (parsedMessage.type === "bot_typing_end") {
          setIsBotTyping(false);
          
          // Clear timeout
          if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
            typingTimeoutRef.current = null;
          }
        }
        
        if (parsedMessage.type === "message") {
          event$.emit({
            type: "refresh",
          });
        }
      },
      shouldReconnect: () => {
        // Automatically reconnect on close
        return !!conversationId;
      },
    }
  );

  const mappedMessages = useMemo(() => {
    return (
      data?.messages.map((msg) => {
        let textContent: string = "";
        if (typeof msg.content === "string") {
          textContent = msg.content;
        } else if (Array.isArray(msg.content)) {
          textContent = msg.content[0].text; // Assuming the first item is the main text
        }

        return {
          id: msg.id,
          content: textContent,
          conversationId: data.conversation.id,
          isUserMessage: msg.isUser,
          timestamp: msg.timestamp,
        } as Message;
      }) || []
    );
  }, [data, conversationId]);

  useAsyncEffect(async () => {
    if (!mappedMessages || mappedMessages.length === 0) return;

    await db.messages.bulkPut(mappedMessages);
  }, [mappedMessages]);

  const sendMessage = (content: string, id?: string) => {
    const newMessage: Message = {
      id: id ?? v7(),
      conversationId: conversationId!,
      content,
      isUserMessage: true,
      timestamp: Date.now(),
    };

    putMessage(newMessage);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    messages: messages ?? [],
    sendMessage,
    isBotTyping,
  };
};
