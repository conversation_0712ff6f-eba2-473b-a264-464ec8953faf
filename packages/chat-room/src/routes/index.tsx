import { db } from "@/db";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect } from "react";
import { v7 } from "uuid";
import { ChatLayout } from "../components/ChatLayout";

export const Route = createFileRoute("/")({
  component: App,
});

async function initClientId() {
  const c = await db.client.limit(1).first();

  console.log("Client:", c);

  if (!c) {
    const newClient: { id: string } = { id: v7() }; // Generate a new client ID
    await db.client.add(newClient);
  }
}

function App() {
  useEffect(() => {
    // This effect runs once when the component mounts
    // You can perform any initialization logic here if needed

    initClientId().catch((error) => {
      console.error("Failed to initialize client ID:", error);
    });
  }, []);

  return <ChatLayout />;
}
