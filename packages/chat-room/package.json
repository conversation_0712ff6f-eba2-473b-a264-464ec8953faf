{"name": "chat-room", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "preview": "bun run build && vite preview", "deploy": "bun run build && wrangler deploy", "deploy:konsultan-cerai": "bun --env-file=env.konsultan-perceraian run build && CLOUDFLARE_ENV=konsultan-cerai wrangler deploy"}, "dependencies": {"@cloudflare/vite-plugin": "^1.5.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.80.6", "@tanstack/react-router": "^1.120.18", "@tanstack/react-router-devtools": "^1.120.18", "@tanstack/router-plugin": "^1.120.18", "@uiw/react-markdown-preview": "^5.1.4", "ahooks": "^3.8.5", "daisyui": "^5.0.43", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-use-websocket": "^4.13.0", "tailwindcss": "^4.1.8", "uuid": "^11.1.0", "wrangler": "^4.19.1"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.1", "jsdom": "^26.1.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.2", "web-vitals": "^4.2.4"}}