import { desc } from 'drizzle-orm';
import { index, integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { v7 } from 'uuid';

export const conversationsTable = sqliteTable('conversations', {
	id: text('id')
		.primaryKey()
		.$defaultFn(() => v7()),
	channel_type: text('channel_type')
		.$type<'whatsapp' | 'web'>()
		.notNull()
		.$defaultFn(() => 'whatsapp'),
	external_id: text('external_id').unique().notNull(),
	created_at: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
	updated_at: integer('updated_at', { mode: 'timestamp' })
		.$defaultFn(() => new Date())
		.$onUpdateFn(() => new Date()),
});

export const messagesTable = sqliteTable(
	'messages',
	{
		id: text('id')
			.primaryKey()
			.$defaultFn(() => v7()),
		conversation_id: text('conversation_id')
			.references(() => conversationsTable.id, { onDelete: 'cascade' })
			.notNull(),
		is_external: integer('is_external', { mode: 'boolean' }).$defaultFn(() => false),
		is_ai: integer('is_ai', { mode: 'boolean' }).$defaultFn(() => false),
		is_system_message: integer('is_system_message', { mode: 'boolean' }).$defaultFn(() => false),
		content: text('content').notNull(),
		is_read: integer('is_read', { mode: 'boolean' }).$defaultFn(() => false),
		read_by: text('read_by', { mode: 'json' })
			.$type<string[]>()
			.$defaultFn(() => []),
		created_at: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
		updated_at: integer('updated_at', { mode: 'timestamp' })
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(t) => [index('conversation_id_is_system_message_index').on(t.conversation_id, t.is_system_message, desc(t.created_at))]
);
