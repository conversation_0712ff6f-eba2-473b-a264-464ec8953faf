import { and, asc, eq } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/d1';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { parse } from 'superjson';
import { v7 } from 'uuid';
import { AppEnv } from './binding';
import * as schema from './db/schema';

const app = new Hono<AppEnv>();

export * from './durableobject/ChatRoom';
export * from './workflow/HandleChat';

type WahaWebhookBody = {
	id: string;
	timestamp: number;
	session: string;
	event: string;
	payload: {
		id: string;
		timestamp: number;
		from: string;
		fromMe: boolean;
		source: string;
		to: string;
		participant: string;
		body: string;
		hasMedia: boolean;
		author: string;
	};
	me: {
		id: string;
		pushName: string;
	};
	environment: {
		version: string;
		engine: string;
		tier: string;
		browser: string;
	};
};

app.use(cors({ origin: '*' }));

app.post('/webhook/waha', async (c) => {
	const body = (await c.req.json()) as WahaWebhookBody;
	console.log('Received webhook:', body);
	const {
		event,
		session,
		payload: { id, from, body: text, to, fromMe },
	} = body;

	if (event !== 'message') {
		return c.json({ status: 'ignored', message: 'Not a message event' }, 200);
	}

	if (fromMe) {
		return c.json({ status: 'ignored', message: 'Message from self' }, 200);
	}

	c.executionCtx.waitUntil(
		c.env.HANDLE_CHAT.create({
			params: {
				from,
				to,
				session,
				channelType: 'whatsapp',
				body: text,
				chatId: id,
			},
		}),
	);

	// Here you would handle the incoming webhook from WhatsApp
	// For example, you could log the message or process it further

	return c.json({ status: 'success', message: 'Webhook received' });
});

app.get('/ws/rooms/:roomId', (c) => {
	const roomId = c.req.param('roomId');
	if (!roomId) {
		return c.text('Room ID is required', 400);
	}

	const clientId = c.req.query('clientId');
	if (!clientId) {
		return c.text('Client ID is required', 400);
	}

	const durableObjectId = c.env.CHATROOM_SERVER.idFromName(roomId);
	const durableObjectStub = c.env.CHATROOM_SERVER.get(durableObjectId);

	return durableObjectStub.fetch(c.req.raw);
});

type SendMessageRequest = {
	messageId?: string;
	content: string;
};

app.post('/rooms/:roomId/messages', async (c) => {
	const roomId = c.req.param('roomId');
	if (!roomId) {
		return c.text('Room ID is required', 400);
	}

	const body = await c.req.json<SendMessageRequest>();

	if (!body.content) {
		return c.text('Message content is required', 400);
	}

	const msgId = body.messageId ?? v7();

	c.executionCtx.waitUntil(
		c.env.HANDLE_CHAT.create({
			params: {
				from: roomId,
				to: roomId,
				session: roomId,
				channelType: 'web',
				body: body.content,
				chatId: msgId,
				messageId: msgId,
			},
		}),
	);

	return c.json({ status: 'success', messageId: msgId });
});

app.get('/rooms/:roomId/messages', async (c) => {
	const roomId = c.req.param('roomId');
	if (!roomId) {
		return c.text('Room ID is required', 400);
	}

	const db = drizzle(c.env.DB, {
		schema,
	});

	const conversation = await db.query.conversationsTable.findFirst({
		where: eq(schema.conversationsTable.external_id, `web:${roomId}`),
	});

	if (!conversation) {
		return c.text('Conversation not found', 404);
	}

	const messages =
		(await db.query.messagesTable.findMany({
			where: and(eq(schema.messagesTable.conversation_id, conversation.id), eq(schema.messagesTable.is_system_message, false)),
			orderBy: [asc(schema.messagesTable.id)],
		})) ?? [];

	return c.json({
		status: 'success',
		data: {
			conversation: {
				id: conversation.external_id.replace('web:', ''),
				channelType: conversation.channel_type,
				timestamp: conversation.created_at?.getTime(),
			},
			messages: messages.map((msg) => ({
				id: msg.id,
				content: parse(msg.content),
				timestamp: msg.created_at?.getTime(),
				isUser: msg.is_external,
				readBy: msg.read_by,
			})),
		},
	});
});

export default app;
