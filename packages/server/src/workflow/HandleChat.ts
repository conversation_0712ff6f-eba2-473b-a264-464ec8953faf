import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { CoreMessage, generateText } from 'ai';
import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from 'cloudflare:workers';
import { asc, eq } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/d1';
import { parse, stringify } from 'superjson';
import { v7 } from 'uuid';
import * as schema from '../db/schema';

export type HandleChatParams = {
	chatId: string;
	from: string;
	to: string;
	body: string;
	messageId?: string;
	session: string;
	channelType: 'whatsapp' | 'web';
};

export class HandleChat extends WorkflowEntrypoint<Env, HandleChatParams> {
	async run(params: WorkflowEvent<HandleChatParams>, step: WorkflowStep) {
		const { from, body, session, channelType, messageId } = params.payload;

		if (channelType === 'web') {
			await step.do('broadcast typing start', async () => {
				const durableObjectId = this.env.CHATROOM_SERVER.idFromName(from);
				const durableObjectStub = this.env.CHATROOM_SERVER.get(durableObjectId);

				await durableObjectStub.fetch(
					new Request('https://localhost/typing-start', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
					}),
				);
			});
		}

		const converstation = await step.do('get or create conversation', async () => {
			const db = drizzle(this.env.DB, {
				schema,
			});

			const conv = await db.query.conversationsTable.findFirst({
				where: eq(schema.conversationsTable.external_id, `${channelType}:${from}`),
			});

			if (!conv) {
				// If conversation does not exist, create a new one
				const conv = await db
					.insert(schema.conversationsTable)
					.values({
						channel_type: channelType,
						external_id: `${channelType}:${from}`,
					})
					.returning();

				return stringify(conv[0]);
			}

			return stringify(conv);
		});

		const userMessages = await step.do('save and fetch user messages', async () => {
			const conversation = parse<typeof schema.conversationsTable.$inferSelect>(converstation);

			const db = drizzle(this.env.DB, {
				schema,
			});

			const [, messages] = await db.batch([
				db.insert(schema.messagesTable).values({
					id: messageId ?? v7(),
					conversation_id: conversation.id,
					is_external: true,
					content: stringify(body),
				}),
				db.query.messagesTable.findMany({
					where: eq(schema.messagesTable.conversation_id, conversation.id),
					orderBy: [asc(schema.messagesTable.id)],
				}),
			]);

			return stringify(messages);
		});

		const generateAiResponse = await step.do('generate AI response', async () => {
			const convMessages = parse<(typeof schema.messagesTable.$inferSelect)[]>(userMessages);

			const openai = createOpenAICompatible({
				name: this.env.PROVIDER_AI_NAME,
				apiKey: this.env.PROVIDER_AI_KEY,
				baseURL: this.env.PROVIDER_AI_URL,
			});
			const messages: CoreMessage[] = convMessages.map(
				(msg) =>
					({
						role: msg.is_external ? 'user' : msg.is_system_message ? 'tool' : 'assistant',
						content: parse(msg.content),
					}) as CoreMessage,
			);
			const { text, response } = await generateText({
				model: openai('konsultan-perceraian'),
				messages,
			});

			return stringify({
				text,
				messages: response.messages,
			});
		});

		const lastMessageId = await step.do('save response messages', async () => {
			const db = drizzle(this.env.DB, {
				schema,
			});

			const conversation = parse<typeof schema.conversationsTable.$inferSelect>(converstation);

			const { messages: convMessages } = parse<{
				text: string;
				messages: CoreMessage[];
			}>(generateAiResponse);

			const resp = await db
				.insert(schema.messagesTable)
				.values(
					convMessages.map((msg) => ({
						conversation_id: conversation.id,
						is_external: false,
						is_ai: msg.role === 'assistant',
						content: stringify(msg.content),
						is_system_message: msg.role === 'tool',
					})),
				)
				.returning();

			// return the last message id
			return resp[resp.length - 1].id;
		});

		if (channelType === 'whatsapp') {
			await step.do('send reply to whatsapp', async () => {
				const { text } = parse<{
					text: string;
					messages: CoreMessage[];
				}>(generateAiResponse);

				console.log('Sending reply to WhatsApp:', { from, text, session });

				const url = new URL(`${this.env.WAHA_HOST}/api/sendText`);

				const phone = from.split('@')[0];

				url.searchParams.set('phone', phone);
				url.searchParams.set('text', text);
				url.searchParams.set('session', session);

				const resp = await fetch(url, {
					headers: {
						'X-Api-Key': this.env.WAHA_API_KEY,
					},
				});

				if (!resp.ok) {
					throw new Error(`Failed to send message: ${resp.status} ${resp.statusText} ${await resp.text()}`);
				}
			});
		}

		if (channelType === 'web') {
			// Send typing end event after message is broadcast
			await step.do('broadcast typing end', async () => {
				const durableObjectId = this.env.CHATROOM_SERVER.idFromName(from);
				const durableObjectStub = this.env.CHATROOM_SERVER.get(durableObjectId);

				await durableObjectStub.fetch(
					new Request('https://localhost/typing-end', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
					}),
				);
			});

			await step.do('broadcast to chatroom', async () => {
				const { text } = parse<{
					text: string;
					messages: CoreMessage[];
				}>(generateAiResponse);

				const durableObjectId = this.env.CHATROOM_SERVER.idFromName(from);
				const durableObjectStub = this.env.CHATROOM_SERVER.get(durableObjectId);

				durableObjectStub;

				await durableObjectStub.fetch(
					new Request('https://localhost', {
						method: 'POST',
						body: JSON.stringify({
							content: text,
							id: lastMessageId,
						}),
						headers: {
							'Content-Type': 'application/json',
						},
					}),
				);
			});
		}
	}
}
