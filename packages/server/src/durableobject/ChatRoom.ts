import { DurableObject } from 'cloudflare:workers';

export class <PERSON><PERSON><PERSON><PERSON> extends DurableObject<Env> {
	clients = new Map<string, WebSocket>();

	constructor(ctx: DurableObjectState, env: Env) {
		super(ctx, env);
	}

	async fetch(request: Request): Promise<Response> {
		const url = new URL(request.url);

		if (request.method === 'GET') {
			const clientId = url.searchParams.get('clientId');

			if (!clientId) {
				return new Response('Client ID is required', { status: 400 });
			}

			const wsPair = new WebSocketPair();
			const [server, client] = Object.values(wsPair);

			this.ctx.acceptWebSocket(server);

			return new Response(null, {
				status: 101,
				webSocket: client,
			});
		}

		// Handle typing end event
		if (request.method === 'POST' && url.pathname === '/typing-start') {
			await this.broadcast({
				type: 'bot_typing_start',
			});
			return new Response('Typing end event sent', { status: 200 });
		}

		// Handle typing end event
		if (request.method === 'POST' && url.pathname === '/typing-end') {
			await this.broadcast({
				type: 'bot_typing_end',
			});
			return new Response('Typing end event sent', { status: 200 });
		}

		// Handle regular message
		if (request.method === 'POST') {
			const body = (await request.json()) as {
				content: string;
				id: string;
			};

			if (!body.content) {
				return new Response('Message content is required', { status: 400 });
			}

			await this.broadcast({
				type: 'message',
				content: body.content,
				id: body.id,
			});

			return new Response('Message sent', { status: 200 });
		}

		return new Response('Method not allowed', { status: 405 });
	}

	async broadcast(message: Record<string, string | number | boolean>) {
		for (const ws of this.ctx.getWebSockets()) {
			try {
				ws.send(JSON.stringify(message));
			} catch (err) {
				console.error('Error sending message to WebSocket:', err);
			}
		}
	}

	async webSocketMessage(ws: WebSocket, message: string | ArrayBuffer) {
		if (typeof message === 'string') {
			if (message === 'ping') {
				ws.send('pong');
			}
		}
	}
}
