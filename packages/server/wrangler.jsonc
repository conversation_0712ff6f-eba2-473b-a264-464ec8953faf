/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "../../node_modules/wrangler/config-schema.json",
	"name": "chatbot-server",
	"main": "src/index.ts",
	"compatibility_date": "2025-05-31",
	"placement": {
		"mode": "smart"
	},
	"observability": {
		"enabled": true
	},
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "chatbot",
			"database_id": "605f7ecf-94c3-4ef3-9bd3-45e0ffd9d41f",
			"migrations_dir": "drizzle"
		}
	],
	"kv_namespaces": [
		{
			"binding": "CHATBOT_CHAT_HISTORY",
			"id": "********************************"
		}
	],
	"workflows": [
		{
			"name": "handle-chat",
			"binding": "HANDLE_CHAT",
			"class_name": "HandleChat"
		}
	],
	"durable_objects": {
		"bindings": [
			{
				"name": "CHATROOM_SERVER",
				"class_name": "ChatRoom",
			}
		]
	},
	"migrations": [
		{
			"tag": "v1",
			"new_sqlite_classes": [
				"ChatRoom"
			]
		}
	],
	"vars": {
		"GOOGLE_GENERATIVE_AI_API_KEY": "AIzaSyByMg73WJIZN_yc9yW_L6qSwm5U9P5vguo",
		"WAHA_HOST": "https://waha.tpk.fun",
		"WAHA_API_KEY": "Geiger-Humongous-Niece6",
		"OPENAI_API_KEY": "********************************************************************************************************************************************************************",
		"PROVIDER_AI_NAME": "openwebui",
		"PROVIDER_AI_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImM4NjI0OWRmLWIzMTctNGZmZS1hOTAyLTc0YmUxMjk3YTJkZSJ9.rdoffD0RnlMiBSkjNgLDYhm16GHqbEpTsHy6pVEHL2Y",
		"PROVIDER_AI_URL": "https://chat.tpk.fun/api"
	}
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },
	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */
	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	// "vars": { "MY_VARIABLE": "production_value" },
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */
	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },
	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}