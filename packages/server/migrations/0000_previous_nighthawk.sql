CREATE TABLE `conversations` (
	`id` text PRIMARY KEY NOT NULL,
	`channel_type` text NOT NULL,
	`external_id` text NOT NULL,
	`created_at` integer,
	`updated_at` integer
);
--> statement-breakpoint
CREATE UNIQUE INDEX `conversations_external_id_unique` ON `conversations` (`external_id`);--> statement-breakpoint
CREATE TABLE `messages` (
	`id` text PRIMARY KEY NOT NULL,
	`conversation_id` text NOT NULL,
	`is_external` integer,
	`is_ai` integer,
	`is_system_message` integer,
	`content` text NOT NULL,
	`is_read` integer,
	`read_by` text,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`conversation_id`) REFERENCES `conversations`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `conversation_id_is_system_message_index` ON `messages` (`conversation_id`,`is_system_message`,"created_at" desc);