{"version": "6", "dialect": "sqlite", "id": "77eed984-8a96-4191-862d-fe430b4cd4c3", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"conversations": {"name": "conversations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "channel_type": {"name": "channel_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"conversations_external_id_unique": {"name": "conversations_external_id_unique", "columns": ["external_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "messages": {"name": "messages", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "conversation_id": {"name": "conversation_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_external": {"name": "is_external", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_ai": {"name": "is_ai", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_system_message": {"name": "is_system_message", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_read": {"name": "is_read", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "read_by": {"name": "read_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"conversation_id_is_system_message_index": {"name": "conversation_id_is_system_message_index", "columns": ["conversation_id", "is_system_message", "\"created_at\" desc"], "isUnique": false}}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {"conversation_id_is_system_message_index": {"columns": {"\"created_at\" desc": {"isExpression": true}}}}}}